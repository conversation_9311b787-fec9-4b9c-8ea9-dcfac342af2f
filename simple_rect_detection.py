#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化矩形检测程序
功能：检测矩形，计算内部矩形对角线交点，发送原始坐标到下位机
"""

import cv2
import numpy as np
import json
import os
from serial_comm import SerialComm

# 参数配置文件名
CONFIG_FILE = "simple_rect_params.json"

# 保存参数到文件
def save_params():
    params = {
        'min_area': cv2.getTrackbarPos('Min Area', 'Debug Controls'),
        'max_area': cv2.getTrackbarPos('Max Area', 'Debug Controls'),
        'threshold': cv2.getTrackbarPos('Threshold', 'Debug Controls'),
        'epsilon': cv2.getTrackbarPos('Epsilon', 'Debug Controls'),
        'scale': cv2.getTrackbarPos('Scale', 'Debug Controls')
    }

    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(params, f, indent=2)
        print(f"参数已保存到 {CONFIG_FILE}")
        return True
    except Exception as e:
        print(f"保存参数失败: {e}")
        return False

# 从文件加载参数
def load_params():
    if not os.path.exists(CONFIG_FILE):
        print(f"配置文件 {CONFIG_FILE} 不存在，使用默认参数")
        return False

    try:
        with open(CONFIG_FILE, 'r') as f:
            params = json.load(f)

        # 设置滑动条位置
        cv2.setTrackbarPos('Min Area', 'Debug Controls', params.get('min_area', 100))
        cv2.setTrackbarPos('Max Area', 'Debug Controls', params.get('max_area', 300))
        cv2.setTrackbarPos('Threshold', 'Debug Controls', params.get('threshold', 46))
        cv2.setTrackbarPos('Epsilon', 'Debug Controls', params.get('epsilon', 3))
        cv2.setTrackbarPos('Scale', 'Debug Controls', params.get('scale', 50))

        print(f"参数已从 {CONFIG_FILE} 加载")
        return True
    except Exception as e:
        print(f"加载参数失败: {e}")
        return False

# 创建调试窗口和滑动条
def create_debug_window():
    cv2.namedWindow('Debug Controls', cv2.WINDOW_NORMAL)
    cv2.resizeWindow('Debug Controls', 350, 200)

    # 创建滑动条 - 修复Max Area显示问题
    cv2.createTrackbar('Min Area', 'Debug Controls', 100, 5000, lambda x: None)
    cv2.createTrackbar('Max Area', 'Debug Controls', 300, 50000, lambda x: None)  # 直接设置实际最大值
    cv2.createTrackbar('Threshold', 'Debug Controls', 46, 255, lambda x: None)
    cv2.createTrackbar('Epsilon', 'Debug Controls', 3, 10, lambda x: None)
    cv2.createTrackbar('Scale', 'Debug Controls', 50, 100, lambda x: None)

    # 创建控制面板背景
    control_panel = np.zeros((200, 350, 3), dtype=np.uint8)
    cv2.putText(control_panel, "Parameter Controls", (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(control_panel, "Adjust sliders to tune detection", (10, 60),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)
    cv2.imshow('Debug Controls', control_panel)

# 获取调试参数
def get_debug_params():
    min_area = cv2.getTrackbarPos('Min Area', 'Debug Controls')
    max_area = cv2.getTrackbarPos('Max Area', 'Debug Controls')  # 直接使用滑动条值
    threshold = cv2.getTrackbarPos('Threshold', 'Debug Controls')
    epsilon_factor = cv2.getTrackbarPos('Epsilon', 'Debug Controls') / 100.0  # 除以100
    scale = cv2.getTrackbarPos('Scale', 'Debug Controls')

    return min_area, max_area, threshold, epsilon_factor, scale

class SimpleRectDetector:
    def __init__(self):
        # 串口通信
        self.serial_comm = SerialComm(port='/dev/ttyTHS0', baudrate=115200, enable=True)
        self.serial_comm.init()

        # 透视变换相关
        self.perspective_matrix = None
        self.inverse_matrix = None
        self.reference_rect = None
        self.standard_size = (400, 300)  # 标准化矩形大小

        # 图像显示开关
        self.show_debug_images = False # 是否显示所有调试图像

        # 创建调试窗口
        create_debug_window()

        # 尝试加载保存的参数
        load_params()

        print("矩形检测程序启动（透视变换优化版）")
        print("功能：检测双矩形，通过透视变换提高精度，串口0x02命令发送坐标")
        print("窗口说明:")
        print("- Rectangle Detection: 检测结果窗口")
        print("- Debug Controls: 参数调节窗口")
        print("- Grayscale Image: 灰度图像（调试用）")
        print("- Binary Image: 二值化图像（调试用）")
        print("- Perspective Corrected: 透视校正后图像")
        print("- Corrected with Detection: 校正图像中的检测结果")
        print("按键说明:")
        print("  q - 退出程序")
        print("  r - 重置参数")
        print("  s - 保存参数")
        print("  l - 重新加载参数")
        print("  d - 切换调试图像显示")
        print("  h - 显示帮助")
        print("使用滑动条实时调节检测参数")
        print("观察二值化图像确保矩形为白色")

    def sort_rectangle_corners(self, corners):
        """对矩形四个角点进行排序：左上、右上、右下、左下"""
        corners = corners.reshape(-1, 2)

        # 计算质心
        center = np.mean(corners, axis=0)

        # 按角度排序
        angles = np.arctan2(corners[:, 1] - center[1], corners[:, 0] - center[0])
        sorted_indices = np.argsort(angles)

        # 重新排列为：左上、右上、右下、左下
        sorted_corners = corners[sorted_indices]

        # 确保第一个点是左上角（x+y最小）
        sums = np.sum(sorted_corners, axis=1)
        min_idx = np.argmin(sums)
        sorted_corners = np.roll(sorted_corners, -min_idx, axis=0)

        return sorted_corners.astype(np.float32)

    def setup_perspective_transform(self, outer_rect):
        """设置透视变换矩阵"""
        # 对外框角点排序
        src_points = self.sort_rectangle_corners(outer_rect)

        # 目标矩形（标准化）
        dst_points = np.array([
            [0, 0],                                    # 左上
            [self.standard_size[0], 0],               # 右上
            [self.standard_size[0], self.standard_size[1]],  # 右下
            [0, self.standard_size[1]]                # 左下
        ], dtype=np.float32)

        # 计算透视变换矩阵
        self.perspective_matrix = cv2.getPerspectiveTransform(src_points, dst_points)
        self.inverse_matrix = cv2.getPerspectiveTransform(dst_points, src_points)
        self.reference_rect = src_points

        return True

    def apply_perspective_correction(self, image, rect_points):
        """应用透视校正"""
        if self.perspective_matrix is None:
            return image, rect_points

        # 对图像进行透视变换
        corrected_image = cv2.warpPerspective(image, self.perspective_matrix, self.standard_size)

        # 对矩形点进行变换
        if rect_points is not None and len(rect_points) > 0:
            corrected_points = []
            for points in rect_points:
                if len(points) == 4:
                    pts = points.reshape(-1, 1, 2).astype(np.float32)
                    transformed_pts = cv2.perspectiveTransform(pts, self.perspective_matrix)
                    corrected_points.append(transformed_pts.reshape(-1, 2))
        else:
            corrected_points = []

        return corrected_image, corrected_points

    def transform_point_back(self, point):
        """将标准化坐标变换回原始图像坐标"""
        if self.inverse_matrix is None:
            return point

        # 将点转换为齐次坐标
        point_homogeneous = np.array([[[point[0], point[1]]]], dtype=np.float32)

        # 应用逆变换
        original_point = cv2.perspectiveTransform(point_homogeneous, self.inverse_matrix)

        return (int(original_point[0][0][0]), int(original_point[0][0][1]))
    
    def calculate_diagonal_intersection(self, rect_points):
        """计算矩形对角线交点"""
        if len(rect_points) != 4:
            return None
        
        points = rect_points.reshape(-1, 2)
        
        # 找到对角顶点
        sum_coords = points.sum(axis=1)
        top_left_idx = np.argmin(sum_coords)      # 左上角
        bottom_right_idx = np.argmax(sum_coords)  # 右下角
        
        diff_coords = points[:, 0] - points[:, 1]
        top_right_idx = np.argmin(diff_coords)    # 右上角
        bottom_left_idx = np.argmax(diff_coords)  # 左下角
        
        # 获取对角顶点坐标
        top_left = points[top_left_idx]
        bottom_right = points[bottom_right_idx]
        top_right = points[top_right_idx]
        bottom_left = points[bottom_left_idx]
        
        # 计算对角线交点
        x1, y1 = top_left
        x2, y2 = bottom_right
        x3, y3 = top_right
        x4, y4 = bottom_left
        
        # 线段交点公式
        denom = (x1-x2)*(y3-y4) - (y1-y2)*(x3-x4)
        if abs(denom) < 1e-10:
            # 平行线，返回几何中心
            center_x = (x1 + x2 + x3 + x4) / 4
            center_y = (y1 + y2 + y3 + y4) / 4
        else:
            t = ((x1-x3)*(y3-y4) - (y1-y3)*(x3-x4)) / denom
            center_x = x1 + t*(x2-x1)
            center_y = y1 + t*(y2-y1)
        
        return (int(center_x), int(center_y))
    
    def detect_rectangles(self, image, min_area, max_area, threshold, epsilon_factor):
        """检测矩形 - 优化为只检测一个矩形，从上下方向识别"""
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # 二值化
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)

        # 显示调试图像（根据开关控制）
        if self.show_debug_images:
            cv2.imshow('Grayscale Image', gray)
            cv2.imshow('Binary Image', binary)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        # 筛选矩形，优先选择从上下方向识别的矩形
        best_rectangle = None
        best_score = 0

        for contour in contours:
            area = cv2.contourArea(contour)
            if min_area <= area <= max_area:
                # 多边形逼近
                epsilon = epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)

                if len(approx) == 4:  # 四边形
                    # 计算矩形的方向性评分（优先上下方向）
                    rect = cv2.minAreaRect(contour)
                    angle = rect[2]  # 旋转角度

                    # 计算与垂直方向的偏差（0度或90度为最佳）
                    vertical_deviation = min(abs(angle), abs(angle - 90), abs(angle + 90))

                    # 综合评分：面积权重 + 方向权重
                    orientation_score = 90 - vertical_deviation  # 越接近垂直方向分数越高
                    total_score = area * 0.7 + orientation_score * area * 0.3  # 面积和方向的加权

                    print(f"轮廓面积: {area}, 角度: {angle:.1f}°, 评分: {total_score:.1f}")

                    if total_score > best_score:
                        best_score = total_score
                        best_rectangle = (approx, area)

        # 返回最佳矩形（只返回一个）
        if best_rectangle:
            return [best_rectangle]
        else:
            return []
    
    def process_frame(self, frame):
        """处理单帧图像 - 集成透视变换优化"""
        # 获取调试参数
        min_area, max_area, threshold, epsilon_factor, scale_percent = get_debug_params()

        # 缩放图像
        width = int(frame.shape[1] * scale_percent / 100)
        height = int(frame.shape[0] * scale_percent / 100)
        resized = cv2.resize(frame, (width, height), interpolation=cv2.INTER_AREA)

        # 检测矩形
        rectangles = self.detect_rectangles(resized, min_area, max_area, threshold, epsilon_factor)

        # 绘制结果
        result = resized.copy()
        
        if len(rectangles) >= 2:
            # 双矩形情况：外框和内框 - 集成透视变换优化
            outer_rect = rectangles[0][0]  # 外框（大）
            inner_rect = rectangles[1][0]  # 内框（小）

            # 设置透视变换（基于外框）
            self.setup_perspective_transform(outer_rect)

            # 应用透视校正
            corrected_image, corrected_rects = self.apply_perspective_correction(
                resized, [outer_rect, inner_rect])

            # 在校正后的图像中重新检测（提高精度）
            if len(corrected_rects) >= 2:
                corrected_outer = corrected_rects[0]
                corrected_inner = corrected_rects[1]

                # 在校正图像中计算更精确的对角线交点
                corrected_center = self.calculate_diagonal_intersection(
                    corrected_inner.reshape(-1, 1, 2))

                if corrected_center:
                    # 将校正后的坐标变换回原始图像
                    original_center = self.transform_point_back(corrected_center)
                else:
                    # 备用方案：使用原始检测结果
                    original_center = self.calculate_diagonal_intersection(inner_rect)
            else:
                # 备用方案：使用原始检测结果
                original_center = self.calculate_diagonal_intersection(inner_rect)

            # 绘制矩形轮廓
            cv2.drawContours(result, [outer_rect], -1, (0, 0, 255), 2)  # 红色外框
            cv2.drawContours(result, [inner_rect], -1, (0, 255, 0), 2)  # 绿色内框

            # 显示透视校正后的图像（根据开关控制）
            if corrected_image is not None and self.show_debug_images:
                cv2.imshow('Perspective Corrected', corrected_image)
                if len(corrected_rects) >= 2:
                    corrected_display = corrected_image.copy()
                    cv2.drawContours(corrected_display, [corrected_outer.astype(int)], -1, (0, 0, 255), 2)
                    cv2.drawContours(corrected_display, [corrected_inner.astype(int)], -1, (0, 255, 0), 2)
                    if corrected_center:
                        cv2.circle(corrected_display, corrected_center, 8, (255, 0, 255), -1)
                    cv2.imshow('Corrected with Detection', corrected_display)

            # 使用透视校正后的精确坐标或原始坐标
            if 'original_center' in locals() and original_center:
                center_point = original_center
                method_info = "[透视校正]"
            else:
                center_point = self.calculate_diagonal_intersection(inner_rect)
                method_info = "[原始检测]"

            # 恢复think.py的顶点排序逻辑（用于显示中点）
            def sort_vertices(vertices):
                center_x = sum([p[0][0] for p in vertices]) / 4
                center_y = sum([p[0][1] for p in vertices]) / 4
                import math
                def angle_from_center(point):
                    return math.atan2(point[0][1] - center_y, point[0][0] - center_x)
                return sorted(vertices, key=angle_from_center)

            outer_sorted = sort_vertices(outer_rect)
            inner_sorted = sort_vertices(inner_rect)

            # 计算对应顶点的中点（恢复think.py逻辑）
            for i in range(4):
                outer_x, outer_y = outer_sorted[i][0]
                inner_x, inner_y = inner_sorted[i][0]

                mid_x = (outer_x + inner_x) // 2
                mid_y = (outer_y + inner_y) // 2

                # 绘制中点十字标记
                cross_size = 10
                cv2.line(result, (mid_x-cross_size, mid_y), (mid_x+cross_size, mid_y), (255, 255, 0), 2)
                cv2.line(result, (mid_x, mid_y-cross_size), (mid_x, mid_y+cross_size), (255, 255, 0), 2)

                cv2.putText(result, f"({mid_x},{mid_y})", (mid_x+15, mid_y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

            if center_point:
                # 绘制交点
                cv2.circle(result, center_point, 8, (255, 0, 255), -1)  # 紫色圆点
                cv2.putText(result, f"Center({center_point[0]},{center_point[1]})",
                           (center_point[0]+10, center_point[1]-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)

                # 发送内部矩形对角线坐标到下位机（使用0x02命令）
                self.serial_comm.send_red_laser_point(center_point)

                # 打印坐标
                print(f"内部矩形对角线交点: ({center_point[0]}, {center_point[1]}) {method_info} [已发送0x02命令]")
        
        elif len(rectangles) == 1:
            # 单矩形情况 - 也应用透视校正
            rect = rectangles[0][0]
            cv2.drawContours(result, [rect], -1, (0, 255, 0), 2)  # 绿色

            # 设置透视变换
            self.setup_perspective_transform(rect)

            # 应用透视校正
            corrected_image, corrected_rects = self.apply_perspective_correction(resized, [rect])

            if len(corrected_rects) >= 1:
                # 在校正图像中计算更精确的对角线交点
                corrected_center = self.calculate_diagonal_intersection(
                    corrected_rects[0].reshape(-1, 1, 2))

                if corrected_center:
                    center_point = self.transform_point_back(corrected_center)
                    method_info = "[透视校正]"
                else:
                    center_point = self.calculate_diagonal_intersection(rect)
                    method_info = "[原始检测]"
            else:
                center_point = self.calculate_diagonal_intersection(rect)
                method_info = "[原始检测]"

            # 显示透视校正后的图像（根据开关控制）
            if corrected_image is not None and self.show_debug_images:
                cv2.imshow('Perspective Corrected', corrected_image)
                if len(corrected_rects) >= 1:
                    corrected_display = corrected_image.copy()
                    cv2.drawContours(corrected_display, [corrected_rects[0].astype(int)], -1, (0, 255, 0), 2)
                    if 'corrected_center' in locals() and corrected_center:
                        cv2.circle(corrected_display, corrected_center, 8, (255, 0, 255), -1)
                    cv2.imshow('Corrected with Detection', corrected_display)

            if center_point:
                # 绘制交点
                cv2.circle(result, center_point, 8, (255, 0, 255), -1)
                cv2.putText(result, f"Center({center_point[0]},{center_point[1]})",
                           (center_point[0]+10, center_point[1]-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)

                # 发送矩形对角线坐标到下位机（使用0x02命令）
                self.serial_comm.send_red_laser_point(center_point)
                print(f"矩形对角线交点: ({center_point[0]}, {center_point[1]}) {method_info} [已发送0x02命令]")
        
        # 显示状态信息
        status_text = f"Rectangles: {len(rectangles)}"
        cv2.putText(result, status_text, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # 显示当前参数（简化版）
        param_text = f"Threshold: {threshold}, Scale: {scale_percent}%"
        cv2.putText(result, param_text, (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)

        # 显示透视变换状态
        perspective_status = "Perspective: ON" if self.perspective_matrix is not None else "Perspective: OFF"
        cv2.putText(result, perspective_status, (10, 90),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

        # 显示调试图像开关状态
        debug_status = "Debug Images: ON" if self.show_debug_images else "Debug Images: OFF"
        debug_color = (0, 255, 0) if self.show_debug_images else (0, 0, 255)
        cv2.putText(result, debug_status, (10, 120),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, debug_color, 1)

        return result
    
    def run(self):
        """主运行循环"""
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("无法打开摄像头")
            return
        
        print("摄像头已启动，开始检测...")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("无法读取摄像头画面")
                break
            
            # 处理帧
            result = self.process_frame(frame)
            
            # 显示结果
            cv2.imshow('Rectangle Detection', result)
            
            # 按键检测
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('r'):  # 'r'键重置参数
                cv2.setTrackbarPos('Min Area', 'Debug Controls', 100)
                cv2.setTrackbarPos('Max Area', 'Debug Controls', 300)
                cv2.setTrackbarPos('Threshold', 'Debug Controls', 46)
                cv2.setTrackbarPos('Epsilon', 'Debug Controls', 3)
                cv2.setTrackbarPos('Scale', 'Debug Controls', 50)
                print("参数已重置为默认值")
            elif key == ord('s'):  # 's'键保存参数
                if save_params():
                    print("当前参数已保存，下次启动时会自动加载")
            elif key == ord('l'):  # 'l'键重新加载参数
                if load_params():
                    print("参数已重新加载")
            elif key == ord('d'):  # 'd'键切换调试图像显示
                self.show_debug_images = not self.show_debug_images
                status = "开启" if self.show_debug_images else "关闭"
                print(f"调试图像显示已{status}")

                # 如果关闭显示，销毁调试窗口
                if not self.show_debug_images:
                    cv2.destroyWindow('Grayscale Image')
                    cv2.destroyWindow('Binary Image')
                    cv2.destroyWindow('Perspective Corrected')
                    cv2.destroyWindow('Corrected with Detection')
            elif key == ord('h'):  # 'h'键显示帮助
                print("\n=== 帮助信息 ===")
                print("按键功能:")
                print("  q - 退出程序")
                print("  r - 重置所有参数")
                print("  s - 保存当前参数")
                print("  l - 重新加载参数")
                print("  d - 切换调试图像显示开关")
                print("  h - 显示此帮助")
                print("调节滑动条来改变检测参数:")
                print("- Min Area: 最小轮廓面积")
                print("- Max Area: 最大轮廓面积 (实际值 = 滑动条值 × 100)")
                print("- Threshold: 二值化阈值")
                print("- Epsilon: 多边形逼近精度 (实际值 = 滑动条值 / 100)")
                print("- Scale: 图像缩放比例")
                print("参数会自动保存到 simple_rect_params.json")
                print("================\n")
        
        # 程序退出时自动保存参数
        save_params()

        # 清理资源
        cap.release()
        cv2.destroyAllWindows()
        self.serial_comm.close()
        print("程序已退出")

def main():
    """主函数"""
    detector = SimpleRectDetector()
    detector.run()

if __name__ == "__main__":
    main()
